<?php

declare(strict_types=1);

use App\Modules\Gpo\Services\SpendAnalysisCsvExportService;
use App\Modules\Gpo\Services\SpendAnalysisService;
use Brick\Math\BigDecimal;
use Brick\Money\Money;

describe('SpendAnalysisCsvExportService', function () {
    beforeEach(function () {
        // Create a real instance since SpendAnalysisService is final
        $this->spendAnalysisService = app(SpendAnalysisService::class);
        $this->service = new SpendAnalysisCsvExportService($this->spendAnalysisService);
    });

    describe('formatMoneyAmount', function () {
        it('formats null values as $0.00', function () {
            $reflection = new ReflectionClass($this->service);
            $method = $reflection->getMethod('formatMoneyAmount');
            $method->setAccessible(true);

            $result = $method->invoke($this->service, null);

            expect($result)->toBe('$0.00');
        });

        it('formats zero values as $0.00', function () {
            $reflection = new ReflectionClass($this->service);
            $method = $reflection->getMethod('formatMoneyAmount');
            $method->setAccessible(true);

            $result = $method->invoke($this->service, '0');

            expect($result)->toBe('$0.00');
        });

        it('formats decimal numbers with proper currency formatting', function () {
            $reflection = new ReflectionClass($this->service);
            $method = $reflection->getMethod('formatMoneyAmount');
            $method->setAccessible(true);

            $testCases = [
                ['28152.01', '$28,152.01'],
                ['10801.63', '$10,801.63'],
                ['263717.31', '$263,717.31'],
                ['1000000.50', '$1,000,000.50'],
                ['123.45', '$123.45'],
                ['0.99', '$0.99'],
            ];

            foreach ($testCases as [$input, $expected]) {
                $result = $method->invoke($this->service, $input);
                expect($result)->toBe($expected);
            }
        });

        it('formats BigDecimal objects correctly', function () {
            $reflection = new ReflectionClass($this->service);
            $method = $reflection->getMethod('formatMoneyAmount');
            $method->setAccessible(true);

            // Create BigDecimal objects like those returned by Money::getAmount()
            $bigDecimal1 = BigDecimal::of('28152.01');
            $bigDecimal2 = BigDecimal::of('263717.31');

            $result1 = $method->invoke($this->service, $bigDecimal1);
            $result2 = $method->invoke($this->service, $bigDecimal2);

            expect($result1)->toBe('$28,152.01');
            expect($result2)->toBe('$263,717.31');
        });

        it('handles Money object amounts correctly', function () {
            $reflection = new ReflectionClass($this->service);
            $method = $reflection->getMethod('formatMoneyAmount');
            $method->setAccessible(true);

            // Create Money objects and get their amounts (which are BigDecimal)
            $money1 = Money::ofMinor(2815201, 'USD'); // $28,152.01
            $money2 = Money::ofMinor(26371731, 'USD'); // $263,717.31

            $result1 = $method->invoke($this->service, $money1->getAmount());
            $result2 = $method->invoke($this->service, $money2->getAmount());

            expect($result1)->toBe('$28,152.01');
            expect($result2)->toBe('$263,717.31');
        });
    });
});
